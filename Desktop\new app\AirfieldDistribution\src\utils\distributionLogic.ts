export interface DistributionItem {
  employee: string;
  vehicle: string;
  task: string;
}

// Employee list
const EMPLOYEES = [
  'Checker 1',
  'Checker 2',
  'Ops 1',
  'Ops 2',
  'Ops 3',
  'Ops 4',
  'Ops 5',
  'Ops 6',
  'Ops 7',
  'Ops 8',
  'Ops 9',
];

// Vehicle list
const VEHICLES = [
  'Car 1',
  'Car 2',
  'Car 3',
  'Car 4',
  'Car 5',
];

// Task list
const TASKS = [
  'Inspect Apron Zone 1',
  'Inspect Apron Zone 2',
  'Inspect Runway 09/27',
  'Inspect Runway 15/33',
  'Inspect Taxiway Alpha',
  'Inspect Taxiway Bravo',
  'Inspect Terminal Area',
  'Inspect Cargo Area',
  'Inspect Maintenance Hangar',
  'Inspect Fuel Farm',
  'Inspect Perimeter Fence',
];

/**
 * Shuffles an array using Fisher-Yates algorithm
 */
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Distributes vehicles fairly among employees
 */
function distributeVehicles(employees: string[]): { [employee: string]: string } {
  const shuffledVehicles = shuffleArray(VEHICLES);
  const vehicleAssignments: { [employee: string]: string } = {};
  
  employees.forEach((employee, index) => {
    // Cycle through vehicles if there are more employees than vehicles
    const vehicleIndex = index % shuffledVehicles.length;
    vehicleAssignments[employee] = shuffledVehicles[vehicleIndex];
  });
  
  return vehicleAssignments;
}

/**
 * Assigns tasks to employees, ensuring variety and fair distribution
 */
function distributeTasks(employees: string[]): { [employee: string]: string } {
  const shuffledTasks = shuffleArray(TASKS);
  const taskAssignments: { [employee: string]: string } = {};
  
  employees.forEach((employee, index) => {
    // Assign tasks in order, cycling if needed
    const taskIndex = index % shuffledTasks.length;
    taskAssignments[employee] = shuffledTasks[taskIndex];
  });
  
  return taskAssignments;
}

/**
 * Generates a complete distribution of employees, vehicles, and tasks
 */
export function generateDistribution(): DistributionItem[] {
  // Shuffle employees for random order
  const shuffledEmployees = shuffleArray(EMPLOYEES);
  
  // Distribute vehicles and tasks
  const vehicleAssignments = distributeVehicles(shuffledEmployees);
  const taskAssignments = distributeTasks(shuffledEmployees);
  
  // Create distribution items
  const distribution: DistributionItem[] = shuffledEmployees.map(employee => ({
    employee,
    vehicle: vehicleAssignments[employee],
    task: taskAssignments[employee],
  }));
  
  return distribution;
}

/**
 * Generates team-based distribution where every two employees form a team
 */
export function generateTeamDistribution(): DistributionItem[] {
  const shuffledEmployees = shuffleArray(EMPLOYEES);
  const shuffledTasks = shuffleArray(TASKS);
  const shuffledVehicles = shuffleArray(VEHICLES);
  
  const distribution: DistributionItem[] = [];
  
  for (let i = 0; i < shuffledEmployees.length; i += 2) {
    const employee1 = shuffledEmployees[i];
    const employee2 = shuffledEmployees[i + 1];
    
    const taskIndex = Math.floor(i / 2) % shuffledTasks.length;
    const vehicleIndex = Math.floor(i / 2) % shuffledVehicles.length;
    
    const task = shuffledTasks[taskIndex];
    const vehicle = shuffledVehicles[vehicleIndex];
    
    // Add first team member
    distribution.push({
      employee: employee1,
      vehicle,
      task: `${task} (Team Leader)`,
    });
    
    // Add second team member if exists
    if (employee2) {
      distribution.push({
        employee: employee2,
        vehicle,
        task: `${task} (Team Member)`,
      });
    }
  }
  
  return distribution;
}

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../App';
import { generateDistribution, DistributionItem } from '../utils/distributionLogic';

type DistributionScreenRouteProp = RouteProp<RootStackParamList, 'Distribution'>;

interface Props {
  route: DistributionScreenRouteProp;
}

const DistributionScreen: React.FC<Props> = ({ route }) => {
  const { shift } = route.params;
  const [distributions, setDistributions] = useState<DistributionItem[]>([]);

  useEffect(() => {
    generateNewDistribution();
  }, []);

  const generateNewDistribution = () => {
    const newDistribution = generateDistribution();
    setDistributions(newDistribution);
  };

  const renderDistributionItem = ({ item }: { item: DistributionItem }) => (
    <View style={styles.distributionCard}>
      <View style={styles.employeeSection}>
        <Text style={styles.employeeName}>{item.employee}</Text>
        <Text style={styles.shiftBadge}>Shift {shift}</Text>
      </View>
      <View style={styles.assignmentSection}>
        <Text style={styles.task}>{item.task}</Text>
        <Text style={styles.vehicle}>🚗 {item.vehicle}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Distribution Results</Text>
        <Text style={styles.subtitle}>Shift {shift} - Task Assignments</Text>
      </View>

      <FlatList
        data={distributions}
        renderItem={renderDistributionItem}
        keyExtractor={(item, index) => index.toString()}
        style={styles.list}
        showsVerticalScrollIndicator={false}
      />

      <TouchableOpacity
        style={styles.regenerateButton}
        onPress={generateNewDistribution}
      >
        <Text style={styles.regenerateButtonText}>🔄 Regenerate Distribution</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e40af',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 5,
  },
  list: {
    flex: 1,
    padding: 15,
  },
  distributionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#2563eb',
  },
  employeeSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  employeeName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  shiftBadge: {
    backgroundColor: '#dbeafe',
    color: '#1e40af',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    fontSize: 12,
    fontWeight: '600',
  },
  assignmentSection: {
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
    paddingTop: 10,
  },
  task: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 5,
    fontWeight: '500',
  },
  vehicle: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  regenerateButton: {
    backgroundColor: '#059669',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 32,
    margin: 20,
    alignItems: 'center',
    shadowColor: '#059669',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  regenerateButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default DistributionScreen;
